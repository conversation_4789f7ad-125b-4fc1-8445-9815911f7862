<?php
defined('_JEXEC') or die('Restricted access');

// Import required classes
jimport('joomla.plugin.helper');
jimport('mrzen.helpers.ZenPriceHelper');
jimport('joomla.application.component.model');
jimport('joomla.application.component.helper');
use Jo<PERSON><PERSON>\Registry\Registry as JRegistry;
use Joom<PERSON>\CMS\Factory as JFactory;
use Jo<PERSON><PERSON>\CMS\Router\Route as JRoute;
use <PERSON><PERSON><PERSON>\CMS\Language\Text as JText;

// Create helper instance
$helper = new ZenbaseCustomHelpers();

// Check if this holiday has special payment terms (minimum_number tag)
$hasMinimumNumberTag = false;
$tagsData = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $this->item->id, 'tags');
if ($tagsData && isset($tagsData['tags']) && isset($tagsData['tags']->items)) {
    foreach ($tagsData['tags']->items as $tagItem) {
        $tagContent = trim($tagItem->content);
        $decodedTag = json_decode($tagContent, true);
        if ($decodedTag && isset($decodedTag['minimum_number']) && $decodedTag['minimum_number'] === 'true') {
            $hasMinimumNumberTag = true;
            break;
        }
    }
}
?>

<!-- Save a Space Modal -->
<style>
#saveSpaceModal {
  display: none; position: fixed; z-index: 1050; left: 0; top: 0; width: 100vw; height: 100vh; overflow: auto;
  background: rgba(0,0,0,0.5); align-items: center; justify-content: center;
}
#saveSpaceModal .modal-content {
  background: #fff; margin: auto; padding: 0; border-radius: 8px; max-width: 700px; width: 90vw; height: 80vh; display: flex; flex-direction: column;overflow: hidden;
}
#saveSpaceModal .modal-header {
  display: flex; flex-direction: row; justify-content: space-between; align-items: center; padding: 0.5rem 1rem 0.25rem 1rem; border-bottom: 1px solid #eee; min-height: 48px;
}
#saveSpaceModal .modal-title {
  font-size: 1.25rem; font-weight: 600; color: #222;
}
#saveSpaceModal .modal-close {
  background: none; border: none; font-size: 2rem; color: #222; cursor: pointer; line-height: 1; padding: 0 0.5rem;
  transition: color 0.2s;
  position: static !important;
}
#saveSpaceModal .modal-close:hover, #saveSpaceModal .modal-close:focus {
  color: #d33;
  outline: none;
}
#saveSpaceModal .modal-body {
  flex: 1; overflow: auto; padding: 0; margin: 0;
  display: flex; flex-direction: column;overflow: scroll
}
#saveSpaceModal .modal-body iframe {
  flex: 1 1 auto;
  width: 100%;
  height: 100%;
  min-height: 0;
  margin: 0;
  padding: 0;
  display: block;
  background: #fff;
  border-radius: 0 0 8px 8px;
}
@media (max-width: 600px) {
  #saveSpaceModal .modal-content { max-width: 98vw; height: 90vh; }
}
</style>
<div id="saveSpaceModal" role="dialog" aria-modal="true" aria-labelledby="saveSpaceModalTitle">
  <div class="modal-content">
    <div class="modal-header">
      <span class="modal-title" id="saveSpaceModalTitle">Save a space</span>
      <button class="modal-close" aria-label="Close modal" id="closeSaveSpaceModal">&times;</button>
    </div>
    <div class="modal-body">
      <!-- The iframe will be created dynamically -->
    </div>
  </div>
</div>
<script src="https://link.evertrek.co.uk/js/form_embed.js"></script>
<script>
// Open modal on button click
function openSaveSpaceModal(evt) {
  var btn = evt.currentTarget;
  var tripDate = encodeURIComponent(btn.getAttribute('data-trip-date') || '');
  var tripName = encodeURIComponent(btn.getAttribute('data-trip-name') || '');
  var baseUrl = 'https://link.evertrek.co.uk/widget/form/ookiI6RslR7RX8A57NI4';
  var params = [];
  if (tripDate) params.push('trip_start_date=' + tripDate);
  if (tripName) params.push('trip_selection=' + tripName);
  var src = baseUrl + (params.length ? ('?' + params.join('&')) : '');

  var modalBody = document.querySelector('#saveSpaceModal .modal-body');
  // Remove any existing iframe
  var oldIframe = modalBody.querySelector('iframe');
  if (oldIframe) oldIframe.remove();
  // Create new iframe
  var iframe = document.createElement('iframe');
  iframe.src = src;
  iframe.style.width = '100%';
  iframe.style.height = '100%';
  iframe.style.maxHeight = 'fit-content';
iframe.style.minHeight = '680px';
iframe.style.overflow = 'scroll';
  iframe.style.border = 'none';
  iframe.style.borderRadius = '0 0 8px 8px';
  iframe.setAttribute('allow', 'clipboard-write');
  iframe.setAttribute('title', 'New Save a Space');
  modalBody.appendChild(iframe);

  document.getElementById('saveSpaceModal').style.display = 'flex';
  document.body.style.overflow = 'hidden';
}
function closeSaveSpaceModal() {
  document.getElementById('saveSpaceModal').style.display = 'none';
  document.body.style.overflow = '';
}
document.addEventListener('DOMContentLoaded', function() {
  document.querySelectorAll('.save-space-btn').forEach(function(btn) {
    btn.addEventListener('click', openSaveSpaceModal);
  });
  document.getElementById('closeSaveSpaceModal').addEventListener('click', closeSaveSpaceModal);
  // Close modal on outside click
  document.getElementById('saveSpaceModal').addEventListener('click', function(e) {
    if (e.target === this) closeSaveSpaceModal();
  });
  // Close modal on Esc key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') closeSaveSpaceModal();
  });
});
</script>


<!-- Move mobile tab button before content -->
<button class="d-md-block d-lg-none js-mobile-tabs collapsed" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab03); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab03); ?>-tab-content">
	<span><?php echo $tab03_display; ?></span>
</button>

<div class="<?php echo $contentClass; ?>" id="<?= $helper->toggleData($tab03); ?>-tab-content" role="tabpayment" aria-labelledby="<?= $helper->toggleData($tab03); ?>-tab">
	<div class="zen-holiday__content-box">
		<div class="zen-holiday__content-item">
			<div class="container">
				<div class="d-flex justify-content-between align-items-start mb-4">
					<div>
						<h3 class="section-title">Departure Dates & Prices</h3>
					</div>
				</div>

				<div class="d-flex flex-column flex-lg-row" style="gap: 25px;">
					<div class="flex-grow-1 dates-prices-main">
						<!-- Pricing Table -->
						<div class="zen-accordion" id="dates-pricing-accordion">
							<style>
								.dates-prices-main {
									flex-basis: 60%;
								}
								.dates-prices-sidebar {
									flex-basis: 40%;
								}
								#dates-pricing-accordion > div.accordion-container {
									border: 1px solid #909090;
									border-radius: 10px;
									background: white;
									margin-bottom: 15px;
								}
								#dates-pricing-accordion > div.accordion-container:last-child {
									margin-bottom: 0;
								}
								/* #dates-pricing-accordion > div.accordion-container > div[id^="price-month-title-"] {
									padding: 15px;
								} */
								#dates-pricing-accordion > div.accordion-container > div[id^="price-month-item-"] {
									padding: 0;
									margin: 0;
								}
							</style>
							<?php
							$dateCount = 0;
							$currentYear = date('Y');

							foreach($dates as $year => $dateItem) :
								// Get first date's year to determine if this is current year
								$firstDate = reset($dateItem);
								$yearToCheck = date('Y', strtotime($firstDate->start_date));

								if ($yearToCheck == $currentYear) {
									// Group dates by month for current year
									$monthlyDates = [];
									foreach ($dateItem as $date) {
										// Skip dates that are in the past or today
										$startDate = strtotime($date->start_date);
										$today = strtotime(date('Y-m-d')); // Today at midnight

										if ($startDate > $today) {
											$month = date('F Y', $startDate);
											if (!isset($monthlyDates[$month])) {
												$monthlyDates[$month] = [];
											}
											$monthlyDates[$month][] = $date;
										}
									}

									// Output each month as a separate section
									// Only proceed if there are dates in this month
									if (!empty($monthlyDates)) {
										foreach ($monthlyDates as $month => $dates) :
											if (empty($dates)) continue; // Skip months with no dates
											$accordionClass = ($dateCount == 0) ? "show" : "";
											$accordionExpand = ($dateCount == 0) ? "true" : "false";
											$monthKey = strtolower(str_replace(' ', '-', $month));
							?>
										<div class="accordion-container">
											<div id="price-month-title-<?= $monthKey; ?>">
												<button class="zen-btn--accordion-sub zen-btn--full-size collapsed" data-bs-toggle="collapse" data-bs-target="#price-month-item-<?= $monthKey; ?>" aria-expanded="<?= $accordionExpand; ?>" aria-controls="price-month-item-<?= $monthKey; ?>">
													<span><?php echo $month; ?></span>
												</button>
											</div>
											<div id="price-month-item-<?= $monthKey; ?>" class="collapse accordion-content <?= $accordionClass; ?>" aria-labelledby="price-month-title-<?= $monthKey; ?>">
												<div class="container py-0 pt-3">
													<?php foreach ($dates as $date) : ?>
														<!-- For Each Date Get Start/End Date -->
														<?php
														$depositPrice = 200;
														$start = strtotime($date->start_date);
														$end = strtotime($date->end_date);
														$availability = $date->spaces;
														$capacity = $date->capacity;
														$numberRemaining = $capacity - $availability;
														$datePrice = $date->prices[1];
														$minusDeposit = ($datePrice->value) - $depositPrice;
														$maxMonthlyPaymentNumber = $yearDiff + $monthDiff - 1;
														$todayDate = new DateTime();
														$todayDate->setTime(12, 0, 0, 0);
														$plusMonths = clone $todayDate;
														$plusMonths->modify("+4 months");
														$serviceStart = new DateTime($date->start_date);
														$availableMonths = $helper->monthsBetweenDates($todayDate, $serviceStart);
														?>
														<!-- Date row content -->
														<div class="row px-0 pt-0 pb-2">
															<div class="col-12 d-flex justify-content-between align-items-start mb-3 px-0">
																<!-- Date -->
																<div>
																	<p class="zen-text zen-text--text-lg zen-text--font-semibold mb-0">
																		<?php
																			echo strftime("%A %d", $start) . date("S", $start) . strftime(" %B %Y", $start) . " –<br>";
																			echo strftime("%A %d", $end) . date("S", $end) . strftime(" %B %Y", $end);
																		?>
																	</p>
																</div>

																<!-- Price -->
																<div class="text-end">
																	<p class="zen-text--price mb-0">
																		<?php echo $datePrice->currency_symbol . number_format($datePrice->value) . "pp"; ?>
																	</p>
																	<?php
																	// Check payment plan availability based on holiday tags
																	$paymentPlanAvailable = false;
																	$paymentPlanMonths = 0;

																	if ($hasMinimumNumberTag) {
																		// For holidays with minimum_number tag, payment must be completed 6 months before departure
																		if ($availableMonths >= 6) {
																			$paymentPlanAvailable = true;
																			$paymentPlanMonths = $availableMonths - 6;
																			// Limit payment plan to 18 months maximum
																			$paymentPlanMonths = min($paymentPlanMonths, 18);
																		}
																	} else {
																		// Standard payment terms: booking at least 4 months before, completed 2 months before
																		if ($availableMonths >= 4) {
																			$paymentPlanAvailable = true;
																			$paymentPlanMonths = $availableMonths - 2;
																			// Limit payment plan to 18 months maximum
																			$paymentPlanMonths = min($paymentPlanMonths, 18);
																		}
																	}

																	if ($paymentPlanAvailable) :
																		// Calculate monthly payment amount
																		$monthlyPayment = ceil(($datePrice->value - $depositPrice) / $paymentPlanMonths);
																	?>
																		<p class="mb-0">
																			<span class="zen-text--price-instalment">OR </span>
																			<span class="zen-text--price-instalment-value"><?php echo $datePrice->currency_symbol . number_format($monthlyPayment); ?> / <?php echo $paymentPlanMonths; ?> months</span>
																		</p>
																	<?php endif; ?>
																</div>
															</div>

															<div class="col-12 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center px-0">
																<!-- Status with Icon -->
																<div class="d-flex align-items-center order-2 order-md-1 mt-3 mt-md-0">
																	<?php if ($availability == 0) : ?>
																		<img src="/templates/zenbase/icons/trips/close.svg" alt="Sold Out" width="20" height="20" class="me-2">
																		<span class="zen-text--text-md">Sold out</span>
																	<?php elseif ($availability <= 4) : ?>
																		<img src="/templates/zenbase/icons/trips/warning_amber.svg" alt="Limited Availability" width="20" height="20" class="me-2">
																		<span class="zen-text--text-md text-orange">Limited – only <?php echo $availability; ?> <?php echo $availability == 1 ? 'space' : 'spaces'; ?> available</span>
																	<?php else : ?>
																		<img src="/templates/zenbase/icons/trips/check_circle_sm.svg" alt="Available" width="20" height="20" class="me-2">
																		<span class="zen-text--text-md text-success"><?php echo $availability; ?> spaces available</span>
																	<?php endif; ?>
																</div>

																<!-- Buttons -->
																<div class="d-flex gap-2 order-1 order-md-2">
																	<?php if ($availability > 0) : ?>
																		<button type="button" class="btn btn-outline btn-primary save-space-btn" data-trip-date="<?php echo htmlspecialchars(date('Y-m-d', $start)); ?>" data-trip-name="<?php echo htmlspecialchars($name); ?>">Save a space</button>
																		<a href="<?php echo $bookingPrefix; ?>?sku=holiday:<?= $date->prices[1]->id; ?>" class="btn btn-primary">Book now</a>
																	<?php else : ?>
																		<span class="btn btn-primary disabled">Sold out</span>
																	<?php endif; ?>
																</div>
															</div>
														</div>
													<?php endforeach; ?>
												</div>
											</div>
										</div>
										<?php
										$dateCount++;
									endforeach;
									} // Close the if (!empty($monthlyDates)) block
								} else {
									// Future years - keep as single section per year
									// First check if there are any future dates in this year
									$hasFutureDates = false;
									foreach ($dateItem as $date) {
										$startDate = strtotime($date->start_date);
										$today = strtotime(date('Y-m-d')); // Today at midnight

										if ($startDate > $today) {
											$hasFutureDates = true;
											break;
										}
									}

									// Only display this year if it has future dates
									if ($hasFutureDates) {
										$accordionClass = ($dateCount == 0) ? "show" : "";
										$accordionExpand = ($dateCount == 0) ? "true" : "false";
									?>
									<div class="accordion-container">
										<div id="price-year-title-<?= $year; ?>">
											<button class="zen-btn--accordion-sub zen-btn--full-size collapsed" data-bs-toggle="collapse" data-bs-target="#price-year-item-<?= $year; ?>" aria-expanded="<?= $accordionExpand; ?>" aria-controls="price-year-item-<?= $year; ?>">
												<span><?php echo $year; ?></span>
											</button>
										</div>
										<div id="price-year-item-<?= $year; ?>" class="collapse accordion-content <?= $accordionClass; ?>" aria-labelledby="price-year-title-<?= $year; ?>">
											<div class="container py-0 pt-3">
												<?php
												// Filter out dates that are in the past or today
												$futureDates = [];
												foreach ($dateItem as $date) {
													$startDate = strtotime($date->start_date);
													$today = strtotime(date('Y-m-d')); // Today at midnight

													if ($startDate > $today) {
														$futureDates[] = $date;
													}
												}

												// Only display if there are future dates
												if (!empty($futureDates)) :
												foreach ($futureDates as $date) : ?>
													<!-- For Each Date Get Start/End Date -->
													<?php
													$depositPrice = 200;
													$start = strtotime($date->start_date);
													$end = strtotime($date->end_date);
													$availability = $date->spaces;
													$capacity = $date->capacity;
													$numberRemaining = $capacity - $availability;
													$datePrice = $date->prices[1];
													$minusDeposit = ($datePrice->value) - $depositPrice;
													$maxMonthlyPaymentNumber = $yearDiff + $monthDiff - 1;
													$todayDate = new DateTime();
													$todayDate->setTime(12, 0, 0, 0);
													$plusMonths = clone $todayDate;
													$plusMonths->modify("+4 months");
													$serviceStart = new DateTime($date->start_date);
													$availableMonths = $helper->monthsBetweenDates($todayDate, $serviceStart);
													?>
													<!-- Date row content -->
													<div class="row py-4 pt-0">
														<div class="col-12 d-flex justify-content-between align-items-start mb-3 px-0">
															<!-- Date -->
															<div>
																<p class="zen-text zen-text--text-lg zen-text--font-semibold mb-0">
																	<?php
																		echo strftime("%A %d", $start) . date("S", $start) . strftime(" %B %Y", $start) . " –<br>";
																		echo strftime("%A %d", $end) . date("S", $end) . strftime(" %B %Y", $end);
																	?>
																</p>
															</div>

															<!-- Price -->
															<div class="text-end">
																<p class="zen-text--price mb-0">
																	<?php echo $datePrice->currency_symbol . number_format($datePrice->value) . "pp"; ?>
																</p>
																<?php
																// Check payment plan availability based on holiday tags
																$paymentPlanAvailable = false;
																$paymentPlanMonths = 0;

																if ($hasMinimumNumberTag) {
																	// For holidays with minimum_number tag, payment must be completed 6 months before departure
																	if ($availableMonths >= 6) {
																		$paymentPlanAvailable = true;
																		$paymentPlanMonths = $availableMonths - 6;
																		// Limit payment plan to 18 months maximum
																		$paymentPlanMonths = min($paymentPlanMonths, 18);
																	}
																} else {
																	// Standard payment terms: booking at least 4 months before, completed 2 months before
																	if ($availableMonths >= 4) {
																		$paymentPlanAvailable = true;
																		$paymentPlanMonths = $availableMonths - 2;
																		// Limit payment plan to 18 months maximum
																		$paymentPlanMonths = min($paymentPlanMonths, 18);
																	}
																}

																if ($paymentPlanAvailable) :
																	// Calculate monthly payment amount
																	$monthlyPayment = ceil(($datePrice->value - $depositPrice) / $paymentPlanMonths);
																?>
																	<p class="mb-0">
																		<span class="zen-text--price-instalment">OR </span>
																		<span class="zen-text--price-instalment-value"><?php echo $datePrice->currency_symbol . number_format($monthlyPayment); ?> / <?php echo $paymentPlanMonths; ?> months</span>
																	</p>
																<?php endif; ?>
															</div>
														</div>

														<div class="col-12 d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center px-0">
															<!-- Status with Icon -->
															<div class="d-flex align-items-center order-2 order-md-1 mt-3 mt-md-0">
																<?php if ($availability == 0) : ?>
																	<img src="/templates/zenbase/icons/trips/close.svg" alt="Sold Out" width="20" height="20" class="me-2">
																	<span class="zen-text--text-md">Sold out</span>
																<?php elseif ($availability <= 4) : ?>
																	<img src="/templates/zenbase/icons/trips/warning_amber.svg" alt="Limited Availability" width="20" height="20" class="me-2">
																	<span class="zen-text--text-md text-orange">Limited – only <?php echo $availability; ?> <?php echo $availability == 1 ? 'space' : 'spaces'; ?> available</span>
																<?php else : ?>
																	<img src="/templates/zenbase/icons/trips/check_circle_sm.svg" alt="Available" width="20" height="20" class="me-2">
																	<span class="zen-text--text-md text-success"><?php echo $availability; ?> spaces available</span>
																<?php endif; ?>
															</div>

															<!-- Buttons -->
															<div class="d-flex gap-2 order-1 order-md-2">
																<?php if ($availability > 0) : ?>
																	<button type="button" class="btn btn-outline btn-sm save-space-btn" data-trip-date="<?php echo htmlspecialchars(date('Y-m-d', $start)); ?>" data-trip-name="<?php echo htmlspecialchars($name); ?>">Save a space</button>
																	<a href="<?php echo $bookingPrefix; ?>?sku=holiday:<?= $date->prices[1]->id; ?>" class="zen-btn zen-btn--orange zen-btn--sm">Book now</a>
																<?php else : ?>
																	<span class="zen-btn zen-btn--secondary zen-btn--sm disabled">Sold out</span>
																<?php endif; ?>
															</div>
														</div>
													</div>
												<?php
												endforeach;
												endif; // Close the if (!empty($futureDates)) block
												?>
											</div>
										</div>
									</div>
									<?php
									$dateCount++;
									} // Close the if ($hasFutureDates) block
								}
							endforeach;

							// Check if we have any dates displayed
							if ($dateCount == 0) {
								echo '<div class="text-center py-4">';
								echo '<p class="mb-0">No upcoming dates are currently available. Please check back later or contact us for more information.</p>';
								echo '</div>';
							}
							?>
						</div>
					</div>

					<div class="flex-grow-1 dates-prices-sidebar">
						<div class="zen-card mb-4">
							<div class="d-flex flex-column">
								<?php
								// Get payment terms copy items if they exist
								$paymentTerms = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $this->item->id, 'payment-terms');
								if ($paymentTerms && isset($paymentTerms['payment-terms']) && isset($paymentTerms['payment-terms']->items)) {
									// Use the custom payment terms content
									foreach ($paymentTerms['payment-terms']->items as $item) {
										// Add classes to the content
										$content = $item->content;
										$content = str_replace('<h3>', '<h3 class="mb-4">', $content);
										$content = str_replace('<h5>', '<h5 class="mb-3">', $content);
										$content = str_replace('<p>', '<p class="mb-0">', $content);

										// Split content into sections
										$sections = preg_split('/(<h5[^>]*>.*?<\/h5>\s*<p[^>]*>.*?<\/p>)/s', $content, -1, PREG_SPLIT_DELIM_CAPTURE | PREG_SPLIT_NO_EMPTY);

										// Rebuild content with proper divs
										$content = '';
										foreach ($sections as $index => $section) {
											if (preg_match('/<h5[^>]*>.*?<\/h5>\s*<p[^>]*>.*?<\/p>/s', $section)) {
												// First section gets mb-4, others don't
												$divClass = ($index === 0) ? 'mb-4' : '';
												$content .= '<div class="' . $divClass . '">' . $section . '</div>';
											} else {
												$content .= $section;
											}
										}

										echo $content;
									}
								} else {
									// Fall back to default content
								?>
								<h3 class="mb-4">Payment Flexibility</h3>

								<div class="mb-4">
									<h5 class="mb-3">Save More When You Pay in Full</h5>
									<p class="mb-0">Bag yourself £100 off when you pay for your trip in full using code FULL100 at checkout when you book more than 3 months before departure.</p>
								</div>

								<div>
									<h5 class="mb-3">Make Your Trip More Affordable</h5>
									<?php if ($hasMinimumNumberTag) : ?>
										<p class="mb-0">With our interest-free monthly payments (available on plans up to 18 months for trips booked 6+ months in advance) your dream trip just got more affordable and hassle-free.</p>
									<?php else : ?>
										<p class="mb-0">With our interest-free monthly payments (available on plans up to 18 months for trips booked 4+ months in advance) your dream trip just got more affordable and hassle-free.</p>
									<?php endif; ?>
								</div>
								<?php } ?>
							</div>
						</div>

						<div class="zen-card">
							<div class="d-flex flex-column">
								<h3 class="mb-4">Secure your space</h3>

								<p class="mb-4">Choose 'Book Now' to secure your spot or choose 'Save a Space' and we'll hold your spot for three days.</p>
								<p class="mb-4">Don't see your preferred date? Just get in touch with us and we will see what we can do.</p>

								<div class="d-flex justify-content-start">
									<a href="/contact" class="btn btn-primary">Enquire now</a>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
